import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet, ScrollView, Alert, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import i18n from '../i18n';
import { useSettings } from '../context/SettingsContext';
import * as Localization from 'expo-localization';
import { getShoppingPlatforms, addShoppingPlatform, updateShoppingPlatform, deleteShoppingPlatform } from '../constants/Storage';

interface ShoppingPlatform {
    id: number;
    name: string;
    icon: string;
    is_default: boolean;
    created_at: string;
}

// 推荐购物平台数据 - 使用键值对应翻译
const RECOMMENDED_PLATFORMS = {
    global: [
        { key: 'amazon', icon: '📦' },
        { key: 'ebay', icon: '🛒' },
        { key: 'aliexpress', icon: '🛍️' },
        { key: 'walmart', icon: '🏪' },
        { key: 'target', icon: '🎯' },
        { key: 'bestbuy', icon: '💻' },
        { key: 'costco', icon: '🏬' },
        { key: 'shopee', icon: '🛒' },
        { key: 'lazada', icon: '📱' },
        { key: 'rakuten', icon: '🌸' },
        { key: 'mercadolibre', icon: '🛍️' },
        { key: 'flipkart', icon: '📦' },
        { key: 'zalando', icon: '👗' },
    ],
    china: [
        { key: 'taobao', icon: '🛍️' },
        { key: 'tmall', icon: '🏪' },
        { key: 'jd', icon: '📦' },
        { key: 'pinduoduo', icon: '🍎' },
        { key: 'douyin', icon: '🎵' },
        { key: 'xianyu', icon: '🐟' },
        { key: 'kuaituantuan', icon: '🛒' },
    ]
};

const ShoppingPlatforms = () => {
    const [platforms, setPlatforms] = useState<ShoppingPlatform[]>([]);
    const [showAddModal, setShowAddModal] = useState(false);
    const [editingPlatform, setEditingPlatform] = useState<ShoppingPlatform | null>(null);
    const [platformName, setPlatformName] = useState('');
    const [platformIcon, setPlatformIcon] = useState('🛒');
    const [isPremium, setIsPremium] = useState(false);

    // Import modal states
    const [showImportModal, setShowImportModal] = useState(false);
    const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);

    const { language } = useSettings();

    useEffect(() => {
        checkPremiumStatus();
        loadPlatforms();
    }, []);

    const checkPremiumStatus = async () => {
        // TODO: 实现会员状态检查
        // 暂时设为 false，需要根据实际的会员系统来实现
        setIsPremium(false);
    };

    const loadPlatforms = async () => {
        try {
            const platformsData = await getShoppingPlatforms();
            setPlatforms(platformsData);
        } catch (error) {
            console.error('Failed to load shopping platforms:', error);
        }
    };

    const handleAddPlatform = async () => {
        if (!platformName.trim()) {
            Alert.alert(i18n.t('common.error'), i18n.t('shoppingPlatforms.platformNameRequired'));
            return;
        }

        try {
            if (editingPlatform) {
                // 更新现有平台
                await updateShoppingPlatform(editingPlatform.id, {
                    name: platformName.trim(),
                    icon: platformIcon,
                });
            } else {
                // 添加新平台
                await addShoppingPlatform({
                    name: platformName.trim(),
                    icon: platformIcon,
                    is_default: false,
                });
            }

            resetForm();
            setShowAddModal(false);
            loadPlatforms(); // 重新加载数据
        } catch (error) {
            console.error('Failed to save platform:', error);
            Alert.alert(i18n.t('common.error'), i18n.t('shoppingPlatforms.addPlatformFailed'));
        }
    };

    const handleEditPlatform = (platform: ShoppingPlatform) => {
        if (platform.is_default) {
            Alert.alert(i18n.t('common.error'), 'Default platforms cannot be edited');
            return;
        }
        setEditingPlatform(platform);
        setPlatformName(platform.name);
        setPlatformIcon(platform.icon);
        setShowAddModal(true);
    };

    const handleDeletePlatform = async (platformId: number) => {
        const platform = platforms.find(p => p.id === platformId);
        if (platform?.is_default) {
            Alert.alert(i18n.t('common.error'), 'Default platforms cannot be deleted');
            return;
        }

        Alert.alert(
            i18n.t('shoppingPlatforms.confirmDelete'),
            i18n.t('shoppingPlatforms.confirmDeleteMessage'),
            [
                { text: i18n.t('common.cancel'), style: 'cancel' },
                {
                    text: i18n.t('common.delete'),
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await deleteShoppingPlatform(platformId);
                            loadPlatforms(); // 重新加载数据
                        } catch (error) {
                            console.error('Failed to delete platform:', error);
                            Alert.alert(i18n.t('common.error'), i18n.t('shoppingPlatforms.deletePlatformFailed'));
                        }
                    }
                }
            ]
        );
    };

    const resetForm = () => {
        setPlatformName('');
        setPlatformIcon('🛒');
        setEditingPlatform(null);
    };

    const handleImportPlatforms = async () => {
        if (selectedPlatforms.length === 0) {
            Alert.alert(i18n.t('common.error'), i18n.t('shoppingPlatforms.selectPlatformsToImport'));
            return;
        }

        try {
            for (const platformKey of selectedPlatforms) {
                const platformData = getAllRecommendedPlatforms().find(p => p.key === platformKey);
                if (platformData) {
                    await addShoppingPlatform({
                        name: i18n.t(`shoppingPlatforms.recommendedPlatforms.${platformKey}`),
                        icon: platformData.icon,
                        is_default: false,
                    });
                }
            }

            setSelectedPlatforms([]);
            setShowImportModal(false);
            loadPlatforms(); // 重新加载数据
            Alert.alert(i18n.t('common.success'), i18n.t('shoppingPlatforms.platformsImported'));
        } catch (error) {
            console.error('Failed to import platforms:', error);
            Alert.alert(i18n.t('common.error'), i18n.t('shoppingPlatforms.importFailed'));
        }
    };

    const getAllRecommendedPlatforms = () => {
        return [...RECOMMENDED_PLATFORMS.global, ...RECOMMENDED_PLATFORMS.china];
    };

    const togglePlatformSelection = (platformKey: string) => {
        setSelectedPlatforms(prev =>
            prev.includes(platformKey)
                ? prev.filter(key => key !== platformKey)
                : [...prev, platformKey]
        );
    };

    const renderPlatform = (platform: ShoppingPlatform) => (
        <View key={platform.id} style={styles.platformItem}>
            <View style={styles.platformHeader}>
                <View style={styles.platformIcon}>
                    <Text style={styles.platformIconText}>{platform.icon}</Text>
                </View>
                <View style={styles.platformInfo}>
                    <Text style={styles.platformName}>{platform.name}</Text>
                    {platform.is_default && (
                        <Text style={styles.defaultLabel}>{i18n.t('shoppingPlatforms.defaultPlatforms')}</Text>
                    )}
                </View>
                {!platform.is_default && (
                    <View style={styles.platformActions}>
                        <TouchableOpacity
                            style={styles.actionButton}
                            onPress={() => handleEditPlatform(platform)}
                        >
                            <Ionicons name="pencil-outline" size={20} color="#666" />
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={styles.actionButton}
                            onPress={() => handleDeletePlatform(platform.id)}
                        >
                            <Ionicons name="trash-outline" size={20} color="#dc4446" />
                        </TouchableOpacity>
                    </View>
                )}
            </View>
        </View>
    );

    const renderAddModal = () => (
        <Modal
            visible={showAddModal}
            transparent={true}
            animationType="slide"
            onRequestClose={() => {
                resetForm();
                setShowAddModal(false);
            }}
        >
            <View style={styles.modalOverlay}>
                <View style={styles.modalContent}>
                    <View style={styles.modalHeader}>
                        <Text style={styles.modalTitle}>
                            {editingPlatform ? i18n.t('shoppingPlatforms.editPlatform') : i18n.t('shoppingPlatforms.addPlatform')}
                        </Text>
                        <TouchableOpacity
                            onPress={() => {
                                resetForm();
                                setShowAddModal(false);
                            }}
                            style={styles.modalCloseButton}
                        >
                            <Ionicons name="close" size={24} color="#666" />
                        </TouchableOpacity>
                    </View>

                    <View style={styles.modalBody}>
                        <Text style={styles.inputLabel}>{i18n.t('shoppingPlatforms.platformName')}</Text>
                        <TextInput
                            style={styles.textInput}
                            placeholder={i18n.t('shoppingPlatforms.platformNameRequired')}
                            value={platformName}
                            onChangeText={setPlatformName}
                        />

                        <Text style={styles.inputLabel}>{i18n.t('shoppingPlatforms.platformIcon')}</Text>
                        <View style={styles.iconSelector}>
                            {['🛒', '🛍️', '📱', '💻', '🏪', '🏬', '🎵', '🐟', '📦', '🎁'].map(icon => (
                                <TouchableOpacity
                                    key={icon}
                                    style={[
                                        styles.iconOption,
                                        platformIcon === icon && styles.selectedIcon
                                    ]}
                                    onPress={() => setPlatformIcon(icon)}
                                >
                                    <Text style={styles.iconText}>{icon}</Text>
                                </TouchableOpacity>
                            ))}
                        </View>
                    </View>

                    <View style={styles.modalActions}>
                        <TouchableOpacity
                            style={styles.cancelButton}
                            onPress={() => {
                                resetForm();
                                setShowAddModal(false);
                            }}
                        >
                            <Text style={styles.cancelButtonText}>{i18n.t('common.cancel')}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={styles.confirmButton}
                            onPress={handleAddPlatform}
                        >
                            <Text style={styles.confirmButtonText}>
                                {editingPlatform ? i18n.t('common.edit') : i18n.t('common.add')}
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );

    const renderImportModal = () => (
        <Modal
            visible={showImportModal}
            transparent={true}
            animationType="slide"
            onRequestClose={() => {
                setSelectedPlatforms([]);
                setShowImportModal(false);
            }}
        >
            <View style={styles.modalOverlay}>
                <View style={[styles.modalContent, { maxHeight: '80%' }]}>
                    <View style={styles.modalHeader}>
                        <Text style={styles.modalTitle}>
                            {i18n.t('shoppingPlatforms.importRecommendedPlatforms')}
                        </Text>
                        <TouchableOpacity
                            onPress={() => {
                                setSelectedPlatforms([]);
                                setShowImportModal(false);
                            }}
                            style={styles.modalCloseButton}
                        >
                            <Ionicons name="close" size={24} color="#666" />
                        </TouchableOpacity>
                    </View>

                    <ScrollView style={styles.modalBody}>
                        <Text style={styles.inputLabel}>{i18n.t('shoppingPlatforms.selectPlatformsToImport')}</Text>

                        <View style={styles.platformGrid}>
                            {getAllRecommendedPlatforms().map(platform => (
                                <TouchableOpacity
                                    key={platform.key}
                                    style={[
                                        styles.platformOption,
                                        selectedPlatforms.includes(platform.key) && styles.selectedPlatform
                                    ]}
                                    onPress={() => togglePlatformSelection(platform.key)}
                                >
                                    <Text style={styles.platformOptionIcon}>{platform.icon}</Text>
                                    <Text style={[
                                        styles.platformOptionText,
                                        selectedPlatforms.includes(platform.key) && styles.selectedPlatformText
                                    ]}>
                                        {i18n.t(`shoppingPlatforms.recommendedPlatforms.${platform.key}`)}
                                    </Text>
                                </TouchableOpacity>
                            ))}
                        </View>
                    </ScrollView>

                    <View style={styles.modalActions}>
                        <TouchableOpacity
                            style={styles.cancelButton}
                            onPress={() => {
                                setSelectedPlatforms([]);
                                setShowImportModal(false);
                            }}
                        >
                            <Text style={styles.cancelButtonText}>{i18n.t('common.cancel')}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={styles.confirmButton}
                            onPress={handleImportPlatforms}
                        >
                            <Text style={styles.confirmButtonText}>{i18n.t('shoppingPlatforms.importSelected')}</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );

    return (
        <View style={styles.container}>
            <ScrollView style={styles.scrollView}>
                <View style={styles.buttonRow}>
                    <TouchableOpacity
                        style={styles.addButton}
                        onPress={() => setShowAddModal(true)}
                    >
                        <Ionicons name="add-circle-outline" size={20} color="white" />
                        <Text style={styles.addButtonText}>{i18n.t('shoppingPlatforms.addPlatform')}</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={styles.importButton}
                        onPress={() => setShowImportModal(true)}
                    >
                        <Ionicons name="download-outline" size={20} color="white" />
                        <Text style={styles.importButtonText}>{i18n.t('shoppingPlatforms.oneClickImport')}</Text>
                    </TouchableOpacity>
                </View>

                {platforms.length === 0 ? (
                    <View style={styles.emptyState}>
                        <Ionicons name="storefront-outline" size={64} color="#ccc" />
                        <Text style={styles.emptyText}>{i18n.t('shoppingPlatforms.noPlatforms')}</Text>
                        <Text style={styles.emptySubtext}>{i18n.t('shoppingPlatforms.addFirstPlatform')}</Text>
                    </View>
                ) : (
                    <View style={styles.platformsList}>
                        {platforms.map(renderPlatform)}
                    </View>
                )}
            </ScrollView>

            {renderAddModal()}
            {renderImportModal()}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    scrollView: {
        flex: 1,
        padding: 16,
    },
    addButton: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#FF5722',
        paddingVertical: 14,
        paddingHorizontal: 16,
        borderRadius: 12,
        shadowColor: '#FF5722',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        elevation: 3,
    },
    addButtonText: {
        color: 'white',
        fontSize: 15,
        fontWeight: '600',
        marginLeft: 6,
    },
    emptyState: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 18,
        color: '#666',
        marginTop: 16,
        fontWeight: '500',
    },
    emptySubtext: {
        fontSize: 14,
        color: '#999',
        marginTop: 8,
        textAlign: 'center',
    },
    platformsList: {
        gap: 12,
    },
    platformItem: {
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 16,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    platformHeader: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    platformIcon: {
        width: 48,
        height: 48,
        borderRadius: 24,
        backgroundColor: '#fff3e0',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 12,
    },
    platformIconText: {
        fontSize: 24,
    },
    platformInfo: {
        flex: 1,
    },
    platformName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
        marginBottom: 4,
    },
    defaultLabel: {
        fontSize: 12,
        color: '#FF5722',
        backgroundColor: '#fff3e0',
        paddingHorizontal: 8,
        paddingVertical: 2,
        borderRadius: 4,
        alignSelf: 'flex-start',
    },
    platformActions: {
        flexDirection: 'row',
        gap: 8,
    },
    actionButton: {
        padding: 8,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
    },
    // Modal styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: 'white',
        borderRadius: 16,
        width: '90%',
        maxWidth: 400,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333',
    },
    modalCloseButton: {
        padding: 4,
    },
    modalBody: {
        padding: 20,
    },
    inputLabel: {
        fontSize: 14,
        fontWeight: '500',
        color: '#333',
        marginBottom: 8,
        marginTop: 12,
    },
    textInput: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        fontSize: 16,
        backgroundColor: '#f9f9f9',
    },
    iconSelector: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,
        marginTop: 8,
    },
    iconOption: {
        width: 40,
        height: 40,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 2,
        borderColor: 'transparent',
    },
    selectedIcon: {
        borderColor: '#FF5722',
        backgroundColor: '#fff3e0',
    },
    iconText: {
        fontSize: 20,
    },
    modalActions: {
        flexDirection: 'row',
        padding: 20,
        gap: 12,
    },
    cancelButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
        alignItems: 'center',
    },
    cancelButtonText: {
        fontSize: 16,
        color: '#666',
    },
    confirmButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        backgroundColor: '#FF5722',
        alignItems: 'center',
    },
    confirmButtonText: {
        fontSize: 16,
        color: 'white',
        fontWeight: '500',
    },
    buttonRow: {
        flexDirection: 'row',
        marginBottom: 20,
        gap: 12,
    },
    importButton: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#4CAF50',
        paddingVertical: 14,
        paddingHorizontal: 16,
        borderRadius: 12,
        shadowColor: '#4CAF50',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        elevation: 3,
    },
    importButtonText: {
        color: 'white',
        fontSize: 15,
        fontWeight: '600',
        marginLeft: 6,
    },
    platformGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 12,
        marginTop: 12,
    },
    platformOption: {
        width: '45%',
        backgroundColor: '#f5f5f5',
        borderRadius: 8,
        padding: 12,
        alignItems: 'center',
        borderWidth: 2,
        borderColor: 'transparent',
    },
    selectedPlatform: {
        borderColor: '#FF5722',
        backgroundColor: '#fff3e0',
    },
    platformOptionIcon: {
        fontSize: 24,
        marginBottom: 4,
    },
    platformOptionText: {
        fontSize: 12,
        color: '#333',
        textAlign: 'center',
    },
    selectedPlatformText: {
        color: '#FF5722',
        fontWeight: '500',
    },
});

export default ShoppingPlatforms;
